import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ConfigService } from '../../../core';

export interface VideoRefreshRequest {
  ad_id: string;
}

export interface VideoRefreshResponse {
  success: boolean;
  message?: string;
}

@Injectable({
  providedIn: 'root',
})
export class AdWinnersWebhookService {
  private readonly featureId = 'ad-winners';
  private readonly webhookBasePath = 'webhook-test';

  constructor(
    private http: HttpClient,
    private configService: ConfigService,
  ) {}

  /**
   * Refresh expired video URL by calling n8n webhook
   * Endpoint: {n8nBaseUrl}/webhook-test/ad-winners/ads/video/refresh
   */
  refreshVideoUrl(adId: string): Observable<VideoRefreshResponse> {
    const url = `${this.getWebhookBaseUrl()}/ads/video/refresh`;
    const payload: VideoRefreshRequest = { ad_id: adId };

    console.log('🔄 Refreshing video URL for ad:', adId, 'via webhook:', url);

    return this.http.post<any>(url, payload).pipe(
      map((response) => {
        console.log('✅ Video refresh webhook response:', response);
        return {
          success: true,
          message: response?.message || 'Video URL refreshed successfully',
        };
      }),
      catchError((error) => {
        console.error('❌ Video refresh webhook failed:', error);
        return throwError(() => ({
          success: false,
          message:
            error?.error?.message ||
            error?.message ||
            'Failed to refresh video URL',
        }));
      }),
    );
  }

  /**
   * Generic method for calling other ad-winners webhooks
   * Can be extended for future webhook endpoints
   */
  callWebhook(endpoint: string, payload: any): Observable<any> {
    const url = `${this.getWebhookBaseUrl()}/${endpoint}`;

    console.log(
      '🔗 Calling ad-winners webhook:',
      url,
      'with payload:',
      payload,
    );

    return this.http.post<any>(url, payload).pipe(
      map((response) => {
        console.log('✅ Webhook response:', response);
        return response;
      }),
      catchError((error) => {
        console.error('❌ Webhook call failed:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Get the base webhook URL for ad-winners feature
   */
  private getWebhookBaseUrl(): string {
    const n8nBaseUrl = this.configService.n8nBaseUrl;
    return `${n8nBaseUrl}/${this.webhookBasePath}/${this.featureId}`;
  }
}
