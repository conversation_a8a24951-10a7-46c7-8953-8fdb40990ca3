import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '../../../core';
import { VideoRefreshRequest, VideoRefreshResponse } from '../models';

@Injectable({
  providedIn: 'root',
})
export class AdWinnersWebhookService {
  constructor(
    private http: HttpClient,
    private configService: ConfigService,
  ) {}

  /**
   * Refresh expired video URL by calling n8n webhook
   */
  refreshVideoUrl(adId: string): Observable<VideoRefreshResponse> {
    const url = `${this.configService.n8nBaseUrl}/webhook-test/ad-winners/ads/video/refresh`;
    const payload: VideoRefreshRequest = { ad_id: adId };
    return this.http.post<VideoRefreshResponse>(url, payload);
  }
}
