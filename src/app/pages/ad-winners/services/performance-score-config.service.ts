import { Injectable } from '@angular/core';
import { Observable, from, of } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { SupabaseService } from '../../../core';
import {
  PerformanceScoreConfiguration,
  PerformanceScoreConfigRequest,
  ALL_AVAILABLE_METRICS,
  PerformanceScoreMetric
} from '../models';

@Injectable({
  providedIn: 'root'
})
export class PerformanceScoreConfigService {
  constructor(private supabaseService: SupabaseService) {}

  /**
   * Get all performance score configurations
   */
  getConfigurations(): Observable<PerformanceScoreConfiguration[]> {
    return from(
      this.supabaseService.client
        .from('adw_performance_score_configurations')
        .select('*')
        .order('is_default', { ascending: false })
        .order('is_system', { ascending: false })
        .order('created_at', { ascending: false })
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching performance score configurations:', response.error);
          return [];
        }
        return response.data || [];
      }),
      catchError((error) => {
        console.error('Error fetching performance score configurations:', error);
        return of([]);
      })
    );
  }

  /**
   * Get default configuration
   */
  getDefaultConfiguration(): Observable<PerformanceScoreConfiguration | null> {
    return from(
      this.supabaseService.client
        .from('adw_performance_score_configurations')
        .select('*')
        .eq('is_default', true)
        .single()
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching default configuration:', response.error);
          return null;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error fetching default configuration:', error);
        return of(null);
      })
    );
  }

  /**
   * Get configuration by ID
   */
  getConfigurationById(id: string): Observable<PerformanceScoreConfiguration | null> {
    return from(
      this.supabaseService.client
        .from('adw_performance_score_configurations')
        .select('*')
        .eq('id', id)
        .single()
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching configuration:', response.error);
          return null;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error fetching configuration:', error);
        return of(null);
      })
    );
  }

  /**
   * Create new configuration
   */
  createConfiguration(config: PerformanceScoreConfigRequest): Observable<PerformanceScoreConfiguration | null> {
    const configData = {
      name: config.name,
      description: config.description,
      metrics_config: config.metrics_config,
      is_default: false,
      is_system: false,
      created_by: 'user' // TODO: Replace with actual user ID when auth is implemented
    };

    return from(
      this.supabaseService.client
        .from('adw_performance_score_configurations')
        .insert(configData)
        .select()
        .single()
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error creating configuration:', response.error);
          return null;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error creating configuration:', error);
        return of(null);
      })
    );
  }

  /**
   * Update configuration
   */
  updateConfiguration(id: string, config: PerformanceScoreConfigRequest): Observable<PerformanceScoreConfiguration | null> {
    const configData = {
      name: config.name,
      description: config.description,
      metrics_config: config.metrics_config,
      updated_at: new Date().toISOString()
    };

    return from(
      this.supabaseService.client
        .from('adw_performance_score_configurations')
        .update(configData)
        .eq('id', id)
        .eq('is_system', false) // Prevent updating system configurations
        .select()
        .single()
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error updating configuration:', response.error);
          return null;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error updating configuration:', error);
        return of(null);
      })
    );
  }

  /**
   * Delete configuration
   */
  deleteConfiguration(id: string): Observable<boolean> {
    return from(
      this.supabaseService.client
        .from('adw_performance_score_configurations')
        .delete()
        .eq('id', id)
        .eq('is_system', false) // Prevent deleting system configurations
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error deleting configuration:', response.error);
          return false;
        }
        return true;
      }),
      catchError((error) => {
        console.error('Error deleting configuration:', error);
        return of(false);
      })
    );
  }

  /**
   * Set configuration as default
   */
  setAsDefault(id: string): Observable<boolean> {
    return from(
      // First, set all configurations to not default
      this.supabaseService.client
        .from('adw_performance_score_configurations')
        .update({ is_default: false })
        .gte('created_at', '1900-01-01') // Update all rows (simple condition that matches all)
    ).pipe(
      switchMap((firstResponse) => {
        if (firstResponse.error) {
          throw firstResponse.error;
        }
        // Then set the selected one as default
        return from(
          this.supabaseService.client
            .from('adw_performance_score_configurations')
            .update({ is_default: true })
            .eq('id', id)
        );
      }),
      map((response) => {
        if (response.error) {
          console.error('Error setting default configuration:', response.error);
          return false;
        }
        return true;
      }),
      catchError((error) => {
        console.error('Error setting default configuration:', error);
        return of(false);
      })
    );
  }

  /**
   * Get all available metrics for configuration
   */
  getAvailableMetrics(): PerformanceScoreMetric[] {
    return [...ALL_AVAILABLE_METRICS];
  }

  /**
   * Validate configuration
   */
  validateConfiguration(config: PerformanceScoreConfigRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check name
    if (!config.name || config.name.trim().length === 0) {
      errors.push('Configuration name is required');
    }

    // Check metrics
    if (!config.metrics_config || !config.metrics_config.metrics || config.metrics_config.metrics.length === 0) {
      errors.push('At least one metric must be configured');
    }

    // Check total weight
    const totalWeight = config.metrics_config.metrics.reduce((sum, metric) => sum + metric.weight, 0);
    if (totalWeight !== 100) {
      errors.push('Total weight must equal 100%');
    }

    // Check for duplicate metrics
    const metricNames = config.metrics_config.metrics.map(m => m.metric);
    const uniqueMetrics = new Set(metricNames);
    if (metricNames.length !== uniqueMetrics.size) {
      errors.push('Duplicate metrics are not allowed');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Create configuration from template
   */
  createFromTemplate(templateId: string, newName: string): Observable<PerformanceScoreConfiguration | null> {
    return this.getConfigurationById(templateId).pipe(
      map((template) => {
        if (!template) {
          return null;
        }

        const newConfig: PerformanceScoreConfigRequest = {
          name: newName,
          description: `Based on ${template.name}`,
          metrics_config: template.metrics_config
        };

        return newConfig;
      }),
      catchError(() => of(null))
    );
  }
}
