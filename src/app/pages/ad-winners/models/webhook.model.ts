// Ad Winners Webhook Request/Response Models

export interface VideoRefreshRequest {
  ad_id: string;
}

export interface VideoRefreshResponse {
  success: boolean;
  message?: string;
}

// Generic webhook response
export interface WebhookResponse {
  success: boolean;
  message?: string;
  data?: any;
}

// Future webhook interfaces can be added here
// Example:
// export interface AdAnalyticsRequest {
//   ad_id: string;
//   period: string;
// }
