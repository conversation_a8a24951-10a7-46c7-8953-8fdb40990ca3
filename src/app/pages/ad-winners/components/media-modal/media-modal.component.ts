import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { MessageModule } from 'primeng/message';
import { AdWinnersWebhookService } from '../../services';

@Component({
  selector: 'chm-media-modal',
  standalone: true,
  imports: [CommonModule, DialogModule, ButtonModule, ProgressSpinnerModule, MessageModule],
  template: `
    <p-dialog
      [(visible)]="visible"
      (onHide)="onClose()"
      [modal]="true"
      [closable]="true"
      [draggable]="false"
      [resizable]="false"
      [maximizable]="true"
      styleClass="media-modal"
      [style]="{ width: '90vw', maxWidth: '1200px' }"
    >
      <ng-template pTemplate="header">
        <div class="modal-header">
          <h3>{{ title || 'Media Preview' }}</h3>
        </div>
      </ng-template>

      <div class="media-content">
        <!-- Loading spinner -->
        <div *ngIf="isRefreshingVideo" class="loading-overlay">
          <p-progressSpinner strokeWidth="3" animationDuration="1s"></p-progressSpinner>
          <p>Refreshing video URL...</p>
        </div>

        <!-- Refresh message -->
        <p-message
          *ngIf="refreshMessage"
          [severity]="refreshMessage.severity"
          [text]="refreshMessage.text"
          styleClass="refresh-message">
        </p-message>

        <!-- Video -->
        <video
          *ngIf="videoUrl && !videoError && !isRefreshingVideo"
          [src]="videoUrl"
          class="modal-video"
          [controls]="true"
          [autoplay]="true"
          [muted]="false"
          (error)="onVideoError($event)"
        ></video>

        <!-- Image -->
        <img
          *ngIf="(!videoUrl || videoError) && imageUrl && !imageError && !isRefreshingVideo"
          [src]="imageUrl"
          [alt]="title"
          class="modal-image"
          (error)="onImageError()"
        />

        <!-- Error message -->
        <div
          *ngIf="(videoError && imageError) || (!videoUrl && !imageUrl) && !isRefreshingVideo"
          class="error-message"
        >
          <i class="pi pi-exclamation-triangle"></i>
          <p>Media could not be loaded</p>
          <p-button
            *ngIf="adId && videoUrl && videoError"
            label="Try Refresh Video"
            icon="pi pi-refresh"
            (onClick)="refreshVideoUrl()"
            [loading]="isRefreshingVideo"
            severity="secondary"
            size="small">
          </p-button>
        </div>
      </div>

      <!-- Footer buttons inside dialog content -->
      <div class="modal-footer">
        <p-button
          label="Close"
          icon="pi pi-times"
          (onClick)="onClose()"
          severity="secondary"
        >
        </p-button>
        <div class="footer-actions">
          <p-button
            *ngIf="videoUrl || imageUrl"
            label="Open Media"
            icon="pi pi-external-link"
            (onClick)="openInNewTab()"
            [outlined]="true"
            severity="secondary"
          >
          </p-button>
          <p-button
            *ngIf="adId"
            label="View on Facebook"
            icon="pi pi-facebook"
            (onClick)="openFacebookAd()"
            severity="primary"
          >
          </p-button>
        </div>
      </div>
    </p-dialog>
  `,
  styles: [
    `
      :host ::ng-deep .media-modal .p-dialog-content {
        padding: 0;
        overflow: hidden;
      }

      :host ::ng-deep .media-modal .p-dialog-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e2e8f0;
      }

      .modal-header h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
      }

      .media-content {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 400px;
        max-height: 70vh;
        background: #000;
        position: relative;
      }

      .modal-video {
        width: 100%;
        height: auto;
        max-height: 70vh;
        object-fit: contain;
      }

      .modal-image {
        width: 100%;
        height: auto;
        max-height: 70vh;
        object-fit: contain;
      }

      .error-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        color: #64748b;
        padding: 2rem;
      }

      .error-message i {
        font-size: 3rem;
        color: #ef4444;
      }

      .error-message p {
        margin: 0;
        font-size: 1.1rem;
      }

      .loading-overlay {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        color: #64748b;
        padding: 2rem;
      }

      .loading-overlay p {
        margin: 0;
        font-size: 1.1rem;
      }

      .refresh-message {
        margin: 1rem;
        width: calc(100% - 2rem);
      }

      .modal-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        border-top: 1px solid #e2e8f0;
        background: white;
      }

      .footer-actions {
        display: flex;
        gap: 0.75rem;
        align-items: center;
      }

      /* Responsive */
      @media (max-width: 768px) {
        :host ::ng-deep .media-modal {
          width: 95vw !important;
        }

        .media-content {
          min-height: 300px;
          max-height: 60vh;
        }

        .modal-footer {
          flex-direction: column;
          gap: 0.75rem;
        }

        .footer-actions {
          flex-direction: column;
          width: 100%;
          gap: 0.5rem;
        }

        .footer-actions p-button {
          width: 100%;
        }
      }
    `,
  ],
})
export class MediaModalComponent {
  @Input() visible: boolean = false;
  @Input() videoUrl: string | null = null;
  @Input() imageUrl: string | null = null;
  @Input() title: string = '';
  @Input() adId: string | null = null;

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() videoUrlRefreshed = new EventEmitter<string>();

  videoError = false;
  imageError = false;
  isRefreshingVideo = false;
  refreshMessage: { severity: 'success' | 'error' | 'info'; text: string } | null = null;

  constructor(private webhookService: AdWinnersWebhookService) {}

  onClose(): void {
    this.visible = false;
    this.visibleChange.emit(false);
    this.resetErrors();
  }

  onVideoError(event: any): void {
    console.log('🎬 Video error event:', event);

    // Check if this is a 403 error (expired presigned URL)
    if (this.is403Error(event) && this.adId && !this.isRefreshingVideo) {
      console.log('🔄 Detected 403 error for video, attempting to refresh URL...');
      this.refreshVideoUrl();
    } else {
      this.videoError = true;
    }
  }

  onImageError(): void {
    this.imageError = true;
  }

  /**
   * Check if the error is a 403 (Forbidden) error indicating expired presigned URL
   */
  private is403Error(event: any): boolean {
    // For video elements, we need to check the network error
    // This is a bit tricky as video elements don't expose HTTP status codes directly
    // We'll use a heuristic approach - if the video fails to load and we have an ad_id,
    // we'll assume it might be a 403 and try to refresh
    return true; // For now, we'll always try to refresh if we have an ad_id
  }

  /**
   * Refresh the video URL by calling the webhook
   */
  refreshVideoUrl(): void {
    if (!this.adId || this.isRefreshingVideo) {
      return;
    }

    this.isRefreshingVideo = true;
    this.refreshMessage = null;
    this.videoError = false;

    this.webhookService.refreshVideoUrl(this.adId).subscribe({
      next: (response) => {
        console.log('✅ Video URL refresh successful:', response);
        this.refreshMessage = {
          severity: 'success',
          text: 'Video URL refreshed successfully. Please try playing the video again.'
        };

        // Emit event to parent component to potentially reload the video
        this.videoUrlRefreshed.emit(this.adId!);

        // Clear the message after 5 seconds
        setTimeout(() => {
          this.refreshMessage = null;
        }, 5000);
      },
      error: (error) => {
        console.error('❌ Video URL refresh failed:', error);
        this.refreshMessage = {
          severity: 'error',
          text: error.message || 'Failed to refresh video URL. Please try again later.'
        };
        this.videoError = true;

        // Clear the message after 10 seconds
        setTimeout(() => {
          this.refreshMessage = null;
        }, 10000);
      },
      complete: () => {
        this.isRefreshingVideo = false;
      }
    });
  }

  openInNewTab(): void {
    const url = this.videoUrl || this.imageUrl;
    if (url) {
      window.open(url, '_blank');
    }
  }

  openFacebookAd(): void {
    if (this.adId) {
      const facebookUrl = this.generateFacebookAdUrl(this.adId);
      window.open(facebookUrl, '_blank');
    }
  }

  generateFacebookAdUrl(adId: string): string {
    // Facebook Ads Manager Ad Preview URL format
    return `https://www.facebook.com/adsmanager/manage/adpreview/?ad_id=${adId}`;
  }

  private resetErrors(): void {
    this.videoError = false;
    this.imageError = false;
    this.isRefreshingVideo = false;
    this.refreshMessage = null;
  }
}
