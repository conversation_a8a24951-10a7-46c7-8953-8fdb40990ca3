import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService, SupabaseService } from '../services';

@Injectable()
export class N8nAuthInterceptor implements HttpInterceptor {
  constructor(
    private configService: ConfigService,
    private supabaseService: SupabaseService
  ) {}

  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    // Check if the request is going to n8n
    const n8nBaseUrl = this.configService.n8nBaseUrl;
    
    if (req.url.startsWith(n8nBaseUrl)) {
      // Get current Supabase session token
      const session = this.supabaseService.currentSession;
      const token = session?.access_token;
      
      if (token) {
        // Clone the request and add Authorization header
        const authReq = req.clone({
          setHeaders: {
            Authorization: `Bearer ${token}`
          }
        });
        
        return next.handle(authReq);
      }
    }
    
    // For all other requests, proceed without modification
    return next.handle(req);
  }
}
